/**************************************************************************/
/*!
    @file     readMifareClassicIrq_LowPower.ino
    <AUTHOR> Industries (Modified for Low Power)
	@license  BSD (see license.txt)

    Low Power NFC Card Reader using ESP32 + PN532

    Features:
    - ESP32 Deep Sleep between card detections
    - PN532 Soft Power Down mode
    - IRQ-based wake-up system
    - Minimal power consumption during idle
    - Configurable sleep timeouts

    Power Consumption Optimizations:
    - Deep sleep: ~10µA (ESP32) + ~1µA (PN532 SPD)
    - Active time: <100ms per card read
    - Total average: <50µA for typical usage

    Wiring (ESP32):
    PN532 SDA -> GPIO21 (SDA)
    PN532 SCL -> GPIO22 (SCL)
    PN532 IRQ -> GPIO34 (Input only, no pullup needed)
    PN532 RESET -> GPIO5
    PN532 3.3V -> 3.3V
    PN532 GND -> GND

    Note: GPIO34 is input-only and perfect for IRQ with external pullup
*/
/**************************************************************************/
#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <Adafruit_PN532.h>
#include <esp_sleep.h>
#include <esp_wifi.h>
#include <esp_bt.h>

// =============================================================================
// CONFIGURATION - Low Power Settings
// =============================================================================

// PN532 Pin Configuration (optimized for ESP32)
#define PN532_IRQ   34    // GPIO34 - Input only, external pullup required
#define PN532_RESET 5     // GPIO5 - Can drive high/low for reset control

// Power Management Settings
#define CARD_READ_TIMEOUT_MS    5000    // Max time to wait for card read
#define SLEEP_AFTER_CARD_MS     2000    // Sleep delay after successful card read
#define DEEP_SLEEP_DURATION_US  30000000ULL  // 30 seconds deep sleep (30 * 1000000)
#define MAX_ACTIVE_TIME_MS      10000   // Max active time before forced sleep

// Debug Settings (disable for maximum power savings)
#define ENABLE_SERIAL_DEBUG     true    // Set to false for production
#define SERIAL_BAUD_RATE        115200

// =============================================================================
// GLOBAL VARIABLES
// =============================================================================

Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);

// Power management state
enum PowerState {
  STATE_INITIALIZING,
  STATE_WAITING_FOR_CARD,
  STATE_CARD_DETECTED,
  STATE_PROCESSING_CARD,
  STATE_SLEEP_DELAY,
  STATE_ENTERING_SLEEP
};

PowerState currentState = STATE_INITIALIZING;
unsigned long stateStartTime = 0;
unsigned long lastActivityTime = 0;
bool cardPresent = false;

// Wake-up reason tracking
RTC_DATA_ATTR int bootCount = 0;

// =============================================================================
// POWER MANAGEMENT FUNCTIONS
// =============================================================================

void disableUnusedPeripherals() {
  // Disable WiFi and Bluetooth for maximum power savings
  esp_wifi_stop();
  esp_bt_controller_disable();

  // Disable ADC when not needed
  adc_power_off();

  // Set unused pins to input with pullup to reduce leakage
  for (int i = 0; i < 40; i++) {
    if (i != PN532_IRQ && i != PN532_RESET && i != 21 && i != 22) { // Skip I2C and NFC pins
      pinMode(i, INPUT_PULLUP);
    }
  }
}

void enterDeepSleep() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Entering deep sleep...");
    Serial.flush(); // Ensure all data is sent
  }

  // Configure wake-up source (external wake-up on PN532_IRQ pin)
  esp_sleep_enable_ext0_wakeup((gpio_num_t)PN532_IRQ, 0); // Wake on LOW (card detected)

  // Also enable timer wake-up as backup
  esp_sleep_enable_timer_wakeup(DEEP_SLEEP_DURATION_US);

  // Put PN532 into soft power down mode before ESP32 sleeps
  // Note: This requires custom implementation as Adafruit library doesn't expose SPD

  delay(100); // Allow time for any pending operations
  esp_deep_sleep_start();
}

void printWakeupReason() {
  if (!ENABLE_SERIAL_DEBUG) return;

  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  switch(wakeup_reason) {
    case ESP_SLEEP_WAKEUP_EXT0:
      Serial.println("Wakeup: NFC card detected (IRQ)");
      break;
    case ESP_SLEEP_WAKEUP_TIMER:
      Serial.println("Wakeup: Timer timeout");
      break;
    case ESP_SLEEP_WAKEUP_UNDEFINED:
    default:
      Serial.println("Wakeup: Power on reset");
      break;
  }
}

void setup(void) {
  // Increment boot counter
  ++bootCount;

  // Initialize serial only if debug is enabled
  if (ENABLE_SERIAL_DEBUG) {
    Serial.begin(SERIAL_BAUD_RATE);
    // Don't wait for serial in production for faster startup
    if (bootCount == 1) {
      while (!Serial && millis() < 3000) delay(10);
    }

    Serial.println("\n=== Low Power NFC Reader ===");
    Serial.printf("Boot count: %d\n", bootCount);
    printWakeupReason();
  }

  // Disable unused peripherals for power savings
  disableUnusedPeripherals();

  // Initialize timing
  stateStartTime = millis();
  lastActivityTime = millis();

  // Initialize PN532
  if (!initializeNFC()) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("NFC initialization failed - entering sleep");
    }
    delay(1000);
    enterDeepSleep();
  }

  // Start in waiting state
  currentState = STATE_WAITING_FOR_CARD;
  stateStartTime = millis();

  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Ready - waiting for NFC card...");
  }
}

// =============================================================================
// NFC INITIALIZATION AND MANAGEMENT
// =============================================================================

bool initializeNFC() {
  nfc.begin();

  uint32_t versiondata = nfc.getFirmwareVersion();
  if (!versiondata) {
    return false;
  }

  if (ENABLE_SERIAL_DEBUG) {
    Serial.printf("Found PN532 chip: PN5%02X\n", (versiondata >> 24) & 0xFF);
    Serial.printf("Firmware version: %d.%d\n",
                  (versiondata >> 16) & 0xFF,
                  (versiondata >> 8) & 0xFF);
  }

  // Configure PN532 for low power operation
  nfc.SAMConfig(); // Configure Security Access Module

  return true;
}

bool startListeningToNFC() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Starting NFC detection...");
  }

  // Start passive target detection
  bool result = nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A);

  if (result && ENABLE_SERIAL_DEBUG) {
    Serial.println("Card already present");
  }

  return result;
}

// =============================================================================
// MAIN LOOP - STATE MACHINE
// =============================================================================

void loop(void) {
  unsigned long currentTime = millis();
  unsigned long stateElapsed = currentTime - stateStartTime;
  unsigned long totalActiveTime = currentTime - lastActivityTime;

  // Safety check: Force sleep if active too long
  if (totalActiveTime > MAX_ACTIVE_TIME_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Max active time exceeded - forcing sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }

  switch (currentState) {
    case STATE_WAITING_FOR_CARD:
      handleWaitingForCard();
      break;

    case STATE_CARD_DETECTED:
      handleCardDetected();
      break;

    case STATE_PROCESSING_CARD:
      handleProcessingCard();
      break;

    case STATE_SLEEP_DELAY:
      handleSleepDelay();
      break;

    case STATE_ENTERING_SLEEP:
      enterDeepSleep();
      break;

    default:
      currentState = STATE_WAITING_FOR_CARD;
      stateStartTime = currentTime;
      break;
  }

  // Small delay to prevent excessive CPU usage
  delay(10);
}

// =============================================================================
// STATE MACHINE HANDLERS
// =============================================================================

void handleWaitingForCard() {
  unsigned long currentTime = millis();
  unsigned long stateElapsed = currentTime - stateStartTime;

  // Check IRQ pin for card detection
  if (digitalRead(PN532_IRQ) == LOW) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("IRQ detected - card present");
    }
    currentState = STATE_CARD_DETECTED;
    stateStartTime = currentTime;
    return;
  }

  // Start listening if not already started
  if (stateElapsed > 100) { // Small delay before starting detection
    if (startListeningToNFC()) {
      currentState = STATE_CARD_DETECTED;
      stateStartTime = currentTime;
      return;
    }
  }

  // Timeout - enter sleep to save power
  if (stateElapsed > CARD_READ_TIMEOUT_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("No card detected - entering sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }
}

void handleCardDetected() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Processing detected card...");
  }

  currentState = STATE_PROCESSING_CARD;
  stateStartTime = millis();
}

void handleProcessingCard() {
  uint8_t success = false;
  uint8_t uid[] = { 0, 0, 0, 0, 0, 0, 0 };
  uint8_t uidLength;

  // Read the NFC tag's info
  success = nfc.readDetectedPassiveTargetID(uid, &uidLength);

  if (ENABLE_SERIAL_DEBUG) {
    Serial.println(success ? "Card read successful" : "Card read failed");
  }

  if (success) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Found ISO14443A card");
      Serial.printf("  UID Length: %d bytes\n", uidLength);
      Serial.print("  UID Value: ");
      nfc.PrintHex(uid, uidLength);

      if (uidLength == 4) {
        uint32_t cardid = uid[0];
        cardid <<= 8;
        cardid |= uid[1];
        cardid <<= 8;
        cardid |= uid[2];
        cardid <<= 8;
        cardid |= uid[3];
        Serial.printf("Mifare Classic card ID: %lu\n", cardid);
      }
      Serial.println();
    }
  }

  // Move to sleep delay state
  currentState = STATE_SLEEP_DELAY;
  stateStartTime = millis();
}

void handleSleepDelay() {
  unsigned long stateElapsed = millis() - stateStartTime;

  if (stateElapsed > SLEEP_AFTER_CARD_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Sleep delay complete - entering sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }
}
