/*
 * Low Power NFC Reader Configuration
 * 
 * This header file contains all configuration options for the low-power
 * ESP32 + PN532 NFC reader system.
 * 
 * Modify these values to customize power management behavior for your
 * specific application requirements.
 */

#ifndef LOW_POWER_CONFIG_H
#define LOW_POWER_CONFIG_H

// =============================================================================
// HARDWARE CONFIGURATION
// =============================================================================

// PN532 Pin Assignments (ESP32)
#define PN532_IRQ_PIN       34    // GPIO34 - Input only, requires external pullup
#define PN532_RESET_PIN     5     // GPIO5 - Reset control
#define PN532_SDA_PIN       21    // GPIO21 - I2C Data (default)
#define PN532_SCL_PIN       22    // GPIO22 - I2C Clock (default)

// External pullup resistor value for IRQ pin (informational)
#define IRQ_PULLUP_OHMS     10000 // 10kΩ recommended

// =============================================================================
// POWER MANAGEMENT CONFIGURATION
// =============================================================================

// Sleep and Wake-up Timing
#define CARD_READ_TIMEOUT_MS        5000    // Max time to wait for card detection
#define SLEEP_AFTER_CARD_MS         2000    // Delay before sleep after card read
#define DEEP_SLEEP_DURATION_US      30000000ULL  // 30 seconds (30 * 1000000)
#define MAX_ACTIVE_TIME_MS          10000   // Max active time before forced sleep
#define STARTUP_DELAY_MS            100     // Delay before starting NFC detection

// Power Optimization Levels
#define POWER_LEVEL_MAXIMUM         0       // Maximum power savings
#define POWER_LEVEL_BALANCED        1       // Balanced power/performance
#define POWER_LEVEL_PERFORMANCE     2       // Performance priority

// Current power level (change this to adjust power/performance trade-off)
#define CURRENT_POWER_LEVEL         POWER_LEVEL_BALANCED

// =============================================================================
// DEBUG AND MONITORING CONFIGURATION
// =============================================================================

// Debug Output Control
#define ENABLE_SERIAL_DEBUG         true    // Enable/disable serial output
#define SERIAL_BAUD_RATE           115200   // Serial communication speed
#define SERIAL_TIMEOUT_MS          3000     // Max time to wait for serial connection

// Debug Detail Levels
#define DEBUG_LEVEL_NONE           0        // No debug output
#define DEBUG_LEVEL_BASIC          1        // Basic state changes
#define DEBUG_LEVEL_DETAILED       2        // Detailed operation info
#define DEBUG_LEVEL_VERBOSE        3        // All debug information

#define CURRENT_DEBUG_LEVEL        DEBUG_LEVEL_BASIC

// Performance Monitoring
#define ENABLE_POWER_MONITORING    false    // Enable power consumption tracking
#define ENABLE_TIMING_ANALYSIS     false    // Enable timing measurements
#define ENABLE_BOOT_COUNTER        true     // Track boot/wake-up count

// =============================================================================
// NFC READER CONFIGURATION
// =============================================================================

// PN532 Communication Settings
#define NFC_I2C_FREQUENCY          100000   // 100kHz for power savings (400000 for speed)
#define NFC_COMMAND_TIMEOUT_MS     1000     // Timeout for NFC commands
#define NFC_RETRY_COUNT            3        // Number of retries for failed operations

// Card Detection Settings
#define CARD_DETECTION_RETRIES     2        // Retries for card detection
#define CARD_READ_RETRIES          3        // Retries for card reading
#define CARD_REMOVAL_DELAY_MS      500      // Delay to detect card removal

// Supported Card Types (enable/disable as needed)
#define SUPPORT_MIFARE_CLASSIC     true     // Mifare Classic cards
#define SUPPORT_MIFARE_ULTRALIGHT  true     // Mifare Ultralight cards
#define SUPPORT_NTAG               true     // NTAG cards
#define SUPPORT_ISO14443A          true     // Generic ISO14443A cards

// =============================================================================
// BATTERY AND POWER SUPPLY CONFIGURATION
// =============================================================================

// Battery Monitoring (if implemented)
#define ENABLE_BATTERY_MONITORING  false    // Enable battery voltage monitoring
#define BATTERY_LOW_VOLTAGE_MV     3200     // Low battery threshold (mV)
#define BATTERY_CRITICAL_VOLTAGE_MV 3000    // Critical battery threshold (mV)
#define BATTERY_ADC_PIN            36       // ADC pin for battery monitoring

// Power Supply Requirements
#define MIN_SUPPLY_VOLTAGE_MV      3100     // Minimum supply voltage (mV)
#define MAX_SUPPLY_VOLTAGE_MV      3600     // Maximum supply voltage (mV)

// =============================================================================
// ADVANCED CONFIGURATION
// =============================================================================

// Memory Management
#define ENABLE_MEMORY_OPTIMIZATION true     // Enable memory optimizations
#define STACK_SIZE_BYTES           4096     // Task stack size
#define HEAP_SIZE_BYTES            8192     // Heap size reservation

// Peripheral Control
#define DISABLE_WIFI_ON_STARTUP    true     // Disable WiFi for power savings
#define DISABLE_BLUETOOTH_ON_STARTUP true   // Disable Bluetooth for power savings
#define DISABLE_ADC_ON_STARTUP     true     // Disable ADC for power savings

// GPIO Configuration
#define SET_UNUSED_PINS_PULLUP     true     // Set unused pins to INPUT_PULLUP
#define UNUSED_PIN_START           0        // First unused pin number
#define UNUSED_PIN_END             39       // Last unused pin number

// Exception pins (pins to skip when configuring unused pins)
static const int EXCEPTION_PINS[] = {
    PN532_IRQ_PIN,      // PN532 IRQ
    PN532_RESET_PIN,    // PN532 Reset
    PN532_SDA_PIN,      // I2C SDA
    PN532_SCL_PIN,      // I2C SCL
    1,                  // Serial TX
    3,                  // Serial RX
    0,                  // Boot button
    2,                  // Built-in LED (if used)
    -1                  // End marker
};

// =============================================================================
// CONDITIONAL COMPILATION BASED ON POWER LEVEL
// =============================================================================

#if CURRENT_POWER_LEVEL == POWER_LEVEL_MAXIMUM
    // Maximum power savings
    #undef ENABLE_SERIAL_DEBUG
    #define ENABLE_SERIAL_DEBUG false
    #undef CURRENT_DEBUG_LEVEL
    #define CURRENT_DEBUG_LEVEL DEBUG_LEVEL_NONE
    #undef NFC_I2C_FREQUENCY
    #define NFC_I2C_FREQUENCY 100000
    #undef CARD_READ_TIMEOUT_MS
    #define CARD_READ_TIMEOUT_MS 3000
    
#elif CURRENT_POWER_LEVEL == POWER_LEVEL_PERFORMANCE
    // Performance priority
    #undef NFC_I2C_FREQUENCY
    #define NFC_I2C_FREQUENCY 400000
    #undef CARD_READ_TIMEOUT_MS
    #define CARD_READ_TIMEOUT_MS 10000
    #undef DEEP_SLEEP_DURATION_US
    #define DEEP_SLEEP_DURATION_US 10000000ULL  // 10 seconds
#endif

// =============================================================================
// VALIDATION AND SAFETY CHECKS
// =============================================================================

// Compile-time validation
#if PN532_IRQ_PIN < 32 || PN532_IRQ_PIN > 39
    #error "PN532_IRQ_PIN must be an input-only pin (32-39)"
#endif

#if CARD_READ_TIMEOUT_MS < 1000
    #warning "CARD_READ_TIMEOUT_MS is very short, may miss cards"
#endif

#if DEEP_SLEEP_DURATION_US < 1000000ULL
    #warning "DEEP_SLEEP_DURATION_US is very short, may impact power savings"
#endif

// =============================================================================
// HELPER MACROS
// =============================================================================

// Debug output macros
#if ENABLE_SERIAL_DEBUG && CURRENT_DEBUG_LEVEL >= DEBUG_LEVEL_BASIC
    #define DEBUG_PRINT(x) Serial.print(x)
    #define DEBUG_PRINTLN(x) Serial.println(x)
    #define DEBUG_PRINTF(fmt, ...) Serial.printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(x)
    #define DEBUG_PRINTLN(x)
    #define DEBUG_PRINTF(fmt, ...)
#endif

#if ENABLE_SERIAL_DEBUG && CURRENT_DEBUG_LEVEL >= DEBUG_LEVEL_DETAILED
    #define DEBUG_DETAIL_PRINT(x) Serial.print(x)
    #define DEBUG_DETAIL_PRINTLN(x) Serial.println(x)
    #define DEBUG_DETAIL_PRINTF(fmt, ...) Serial.printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_DETAIL_PRINT(x)
    #define DEBUG_DETAIL_PRINTLN(x)
    #define DEBUG_DETAIL_PRINTF(fmt, ...)
#endif

#if ENABLE_SERIAL_DEBUG && CURRENT_DEBUG_LEVEL >= DEBUG_LEVEL_VERBOSE
    #define DEBUG_VERBOSE_PRINT(x) Serial.print(x)
    #define DEBUG_VERBOSE_PRINTLN(x) Serial.println(x)
    #define DEBUG_VERBOSE_PRINTF(fmt, ...) Serial.printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_VERBOSE_PRINT(x)
    #define DEBUG_VERBOSE_PRINTLN(x)
    #define DEBUG_VERBOSE_PRINTF(fmt, ...)
#endif

// Power level macros
#define IS_MAXIMUM_POWER_SAVINGS() (CURRENT_POWER_LEVEL == POWER_LEVEL_MAXIMUM)
#define IS_BALANCED_MODE() (CURRENT_POWER_LEVEL == POWER_LEVEL_BALANCED)
#define IS_PERFORMANCE_MODE() (CURRENT_POWER_LEVEL == POWER_LEVEL_PERFORMANCE)

#endif // LOW_POWER_CONFIG_H
